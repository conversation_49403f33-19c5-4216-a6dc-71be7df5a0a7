import Joi from "joi";
import { DefaultMessage } from "./message/default.message";

// Regex patterns
const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/; // HH:mm format
const dateRegex = /^(0[1-9]|[12]\d|3[01])-(0[1-9]|1[0-2])-\d{4}$/; // DD-MM-YYYY
const dateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/; // YYYY-MM-DDTHH:mm:ss.sssZ
const nameRegex = /^[A-Za-z ]+$/; // allows only letters and spaces, restrict numbers and special characters

export const TherapistLoginSchema: Joi.Schema = Joi.object({
  email: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Email")),
  password: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Pasword ")),
});

export const SendOtpSchema: Joi.Schema = Joi.object({
  email: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Email")),
});

export const VerifyOtpSchema: Joi.Schema = Joi.object({
  email: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Email")),
  otp: Joi.string().required().messages(DefaultMessage.defaultRequired("Otp")),
});

export const UpdatePasswordSchema: Joi.Schema = Joi.object({
  email: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Email")),
  password: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Password")),
});

export const RecurrenceAmountSchema: Joi.Schema = Joi.object({
  amount: Joi.number()
    .required()
    .messages(DefaultMessage.defaultRequired("Amount")),
});

export const UpdatePayTrackerAmountSchema: Joi.Schema = Joi.object({
  amount: Joi.number()
    .positive()
    .required()
    .messages({
      "number.base": "Amount must be a number",
      "number.positive": "Amount must be greater than 0",
      "any.required": "Amount is required"
    }),
});

export const ClientUpdateSchema: Joi.Schema = Joi.object({
  name: Joi.string().optional(),
  gender: Joi.string().optional().allow("", null),
  phone: Joi.optional().allow("", null),
  age: Joi.string().optional().allow("", null),
  isActive: Joi.boolean(),
  defaultSessionAmount: Joi.string(),
  defaultTimezone: Joi.string(),
});

export const TherapistCalenderEventSchema: Joi.Schema = Joi.object({
  summary: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("summary")),
  location: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("location ")),
  description: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("description ")),
  start: Joi.object()
    .keys({
      dateTime: Joi.date()
        .required()
        .messages(DefaultMessage.defaultRequired("startTime")),
      timeZone: Joi.string()
        .required()
        .messages(DefaultMessage.defaultRequired("timeZone")),
    })
    .required()
    .messages(DefaultMessage.defaultRequired("start")),
  end: Joi.object()
    .keys({
      dateTime: Joi.date()
        .required()
        .messages(DefaultMessage.defaultRequired("startTime")),
      timeZone: Joi.string()
        .required()
        .messages(DefaultMessage.defaultRequired("timeZone")),
    })
    .required()
    .messages(DefaultMessage.defaultRequired("end")),
  emails: Joi.array()
    .items({
      email: Joi.string()
        .required()
        .messages(DefaultMessage.defaultRequired("email")),
    })
    .required()
    .messages(DefaultMessage.defaultRequired("emails")),
});

export const EventIdSchema: Joi.Schema = Joi.object({
  eventIds: Joi.array()
    .required()
    .messages(DefaultMessage.defaultRequired("eventIds")),
});

export const SyncCalendarClientsSchema: Joi.Schema = Joi.object({
  events: Joi.array()
    .items(
      Joi.object({
        calendarEventId: Joi.string()
          .required()
          .messages({ "any.required": "calendarEventId is required." }),

        summary: Joi.string().allow("").required().messages({
          "any.required": "summary is required.",
        }),

        attendee: Joi.string().email().required().messages({
          "any.required": "attendee is required.",
          "string.email": "attendee must be a valid email.",
        }),
      })
    )
    .min(1)
    .required()
    .messages({
      "array.min": "At least one event is required.",
      "any.required": "Events array is required.",
    }),
});

export const BankDetailsSchema: Joi.Schema = Joi.object({
  bankAccountNo: Joi.number()
    .required()
    .messages(DefaultMessage.defaultRequired("bankAccountNo")),
  ifscCode: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("ifscCode")),
  branch: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("branch")),
  bankName: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("bankName")),
  accountHolderName: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("accountHolderName")),
});

export const UpdateTherapistSchema: Joi.Schema = Joi.object({
  email: Joi.string().optional(),
  name: Joi.string().optional(),
  bankDetails: Joi.object()
    .keys({
      upiApprove: Joi.boolean().optional(),
      bankAccountNo: Joi.string().optional(),
      ifscCode: Joi.string().optional(),
      branch: Joi.string().optional(),
      bankName: Joi.string().optional(),
      upiId: Joi.string().optional(),
      accountHolderName: Joi.string().optional(),
    })
    .optional(),
  s3ProfilePhoto: Joi.boolean().optional(),
  settings: Joi.object()
    .keys({
      emailNotification: Joi.boolean().optional(),
      weeklyReportsNotification: Joi.boolean().optional(),
      emailOnSessionConfirmation: Joi.boolean().optional(),
    })
    .optional(),
  identifier: Joi.string().optional(),
  phone: Joi.string().optional(),
  panCard: Joi.string().required(),
  gstNumber: Joi.string().optional(),
  address: Joi.object({
    streetAddress: Joi.string().optional(),
    pincode: Joi.string().required(),
    district: Joi.string().required(),
    state: Joi.string().required(),
  }).optional(),
  oldPassword: Joi.string().optional(),
  password: Joi.string().optional(),
  googleCalendarSynced: Joi.boolean().optional(),
  verificationDetails: Joi.object()
    .keys({
      agePreference: Joi.string().optional(),
      genderPreference: Joi.string().optional(),
      uploadedDocsCount: Joi.number().optional(),
      practicingTitle: Joi.string().optional(),
      clientLoad: Joi.string().optional(),
      yearsOfExperience: Joi.string().optional(),
      featuresNeed: Joi.array().optional(),
      source: Joi.array().optional(),
    })
    .optional(),
  otpData: Joi.object()
    .keys({
      otp: Joi.string().optional(),
      validTill: Joi.optional(),
    })
    .optional(),
  isVerified: Joi.boolean().optional(),
});

export const VerificationTherapistSchema: Joi.Schema = Joi.object({
  agePreference: Joi.string().optional(),
  genderPreference: Joi.string().optional(),
  practicingTitle: Joi.string().optional(),
  clientLoad: Joi.string().optional(),
  yearsOfExperience: Joi.string().optional(),
  featuresNeed: Joi.string().optional(),
  source: Joi.string().optional(),
});

export const VerifyPayment: Joi.Schema = Joi.object({
  paymentId: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Payment Id")),
  orderId: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Order Id")),
  paymentSignature: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Payment Signature")),
});

export const TherapistUpdateSchema: Joi.Schema = Joi.object({
  bankDetails: Joi.object()
    .keys({
      upiApprove: Joi.boolean().optional(),
      bankAccountNo: Joi.any().optional(),
      ifscCode: Joi.string().optional(),
      branch: Joi.string().optional(),
      bankName: Joi.string().optional(),
      upiId: Joi.string().optional(),
      accountHolderName: Joi.string().optional(),
    })
    .optional(),
  settings: Joi.object()
    .keys({
      emailNotification: Joi.boolean().optional(),
      weeklyReportsNotification: Joi.boolean().optional(),
      emailOnSessionConfirmation: Joi.boolean().optional(),
    })
    .optional(),
  verificationDetails: Joi.object()
    .keys({
      agePreference: Joi.string().optional(),
      genderPreference: Joi.array().optional(),
      uploadedDocsCount: Joi.number().optional(),
      practicingTitle: Joi.string().optional(),
      clientLoad: Joi.string().optional(),
      yearsOfExperience: Joi.string().optional(),
      featuresNeed: Joi.array().optional(),
      source: Joi.array().optional(),
      docs: Joi.array().optional(),
      sentForVerification: Joi.boolean().optional(),
    })
    .optional(),
  isVerified: Joi.boolean().optional(),
  isDeleted: Joi.boolean().optional(),
});

export const TherapistOnboardingSchema: Joi.Schema = Joi.object({
  name: Joi.string().pattern(nameRegex).required().messages({
    "string.base": "name must be a string.",
    "string.empty": "name cannot be empty.",
    "any.required": "name is required.",
    "string.pattern.base": "name can only contain letters and spaces",
  }),
  pronouns: Joi.string().required().messages({
    "any.required": "pronouns are required.",
    "string.base": "pronouns must be a string.",
    "string.empty": "pronouns cannot be empty.",
  }),
  yearsOfExperience: Joi.number().integer().min(0).max(50).required().messages({
    "number.base": "yearsOfExperience must be a number.",
    "number.integer": "yearsOfExperience must be a whole number.",
    "number.min": "yearsOfExperience must be at least 0.",
    "number.max": "yearsOfExperience should be at max 50.",
    "any.required": "yearsOfExperience is required.",
  }),
  gender: Joi.string().required().messages({
    "string.base": "gender must be a string.",
    "string.empty": "gender cannot be empty.",
    "any.required": "gender is required.",
  }),
  designation: Joi.string().required().messages({
    "string.base": "designation must be a string.",
    "string.empty": "designation cannot be empty.",
    "any.required": "designation is required.",
  }),
  therapyTypes: Joi.array().items(Joi.string()).min(1).required().messages({
    "array.base": "therapyTypes must be an array.",
    "array.min": "At least one therapy type must be selected.",
    "any.required": "therapyTypes are required.",
    "any.only": "Invalid therapyTypes provided.",
  }),
  languages: Joi.array().items(Joi.string()).min(1).required().messages({
    "array.base": "languages must be an array.",
    "array.min": "At least one language must be selected.",
    "any.required": "languages are required.",
  }),
  minFee: Joi.number().min(500).required().messages({
    "number.base": "minFee must be a number.",
    "number.min": "minFee must be at least 1000.",
    "any.required": "minFee is required.",
  }),
  maxFee: Joi.number()
    .greater(Joi.ref("minFee"))
    .max(10000)
    .required()
    .messages({
      "number.base": "maxFee must be a number.",
      "number.greater": "maxFee must be greater than minFee.",
      "number.max": "maxFee must not exceed 10000.",
      "any.required": "maxFee is required.",
    }),
  location: Joi.array().items(Joi.string()).min(1).required().messages({
    "array.base": "location must be an array.",
    "array.min": "At least one location type must be selected.",
    "any.required": "location is required.",
  }),
  slotType: Joi.array().items(Joi.string()).min(1).required().messages({
    "array.base": "slotType must be an array.",
    "array.min": "At least one slot type must be selected.",
    "any.required": "slotType is required.",
  }),
  // timeZone: Joi.string().required().messages({
  //   "string.base": "timeZone must be a string.",
  //   "string.empty": "timeZone cannot be empty.",
  //   "any.required": "timeZone is required.",
  // }),
  professionalQualification: Joi.string().required().messages({
    "string.base": "professionalQualification must be a string.",
    "string.empty": "professionalQualification cannot be empty.",
    "any.required": "professionalQualification is required.",
  }),
  values: Joi.array().items(Joi.string()).min(1).required().messages({
    "array.base": "values must be an array.",
    "array.min": "At least one value must be selected.",
    "any.required": "Values are required.",
  }),
  concerns: Joi.array().items(Joi.string()).min(1).required().messages({
    "array.base": "concerns must be an array.",
    "array.min": "At least one concern must be selected.",
    "any.required": "concerns are required.",
  }),
  practiceApproach: Joi.string().required().messages({
    "string.base": "practiceApproach must be a string.",
    "string.empty": "practiceApproach cannot be empty.",
    "any.required": "practiceApproach is required.",
  }),
});

export const TherapistProfileUpdateSchema: Joi.Schema = Joi.object({
  name: Joi.string().pattern(nameRegex).messages({
    "string.base": "name must be a string.",
    "string.pattern.base": "name can only contain letters and spaces",
  }),
  pronouns: Joi.string().messages({
    "any.only": "pronouns must be one of the specified options.",
    "string.base": "pronouns must be a string.",
  }),
  yearsOfExperience: Joi.number().integer().min(0).max(50).messages({
    "number.base": "yearsOfExperience must be a number.",
    "number.min": "yearsOfExperience must be at least 0.",
    "number.max": "yearsOfExperience should be at max 50.",
  }),
  gender: Joi.string().messages({
    "string.base": "gender must be a string.",
  }),
  designation: Joi.string().messages({
    "string.base": "designation must be a string.",
  }),
  therapyTypes: Joi.array().items(Joi.string()).messages({
    "array.includes": "therapyTypes must contain only strings.",
    "string.base": "Each item in therapyTypes must be a string.",
  }),
  languages: Joi.array().items(Joi.string()).messages({
    "array.includes": "therapyTypes must contain only strings.",
    "string.base": "Each item in therapyTypes must be a string.",
  }),
  minFee: Joi.number().min(500).messages({
    "number.base": "minFee must be a number.",
    "number.min": "minFee must be at least 1000.",
  }),

  maxFee: Joi.number().greater(Joi.ref("minFee")).max(10000).messages({
    "number.base": "maxFee must be a number.",
    "number.greater": "maxFee must be greater than minFee.",
    "number.max": "maxFee must not exceed 10000.",
  }),
  location: Joi.array().items(Joi.string()).messages({
    "array.includes": "therapyTypes must contain only strings.",
    "string.base": "Each item in therapyTypes must be a string.",
  }),
  slotType: Joi.array().items(Joi.string()).messages({
    "array.includes": "therapyTypes must contain only strings.",
    "string.base": "Each item in therapyTypes must be a string.",
  }),
  // timeZone: Joi.string().messages({
  //   "string.base": "timeZone must be a string.",
  // }),
  professionalQualification: Joi.string().messages({
    "string.base": "professionalQualification must be a string.",
  }),
  values: Joi.array().items(Joi.string()).messages({
    "array.includes": "therapyTypes must contain only strings.",
    "string.base": "Each item in therapyTypes must be a string.",
  }),
  concerns: Joi.array().items(Joi.string()).messages({
    "array.includes": "therapyTypes must contain only strings.",
    "string.base": "Each item in therapyTypes must be a string.",
  }),
  practiceApproach: Joi.string().messages({
    "string.base": "practiceApproach must be a string.",
  }),
  bookingMessage: Joi.string().messages({
    "string.base": "bookingMessage must be a string.",
  }),
});

const timeSlotSchema = Joi.object({
  startTime: Joi.string().pattern(timeRegex).required().messages({
    "string.base": "Start time must be a string.",
    "string.empty": "Start time is required.",
    "string.pattern.base": "Start time must be in HH:mm format (e.g., 08:30).",
    "any.required": "Start time is required.",
  }),
  endTime: Joi.string().pattern(timeRegex).required().messages({
    "string.base": "End time must be a string.",
    "string.empty": "End time is required.",
    "string.pattern.base": "End time must be in HH:mm format (e.g., 09:00).",
    "any.required": "End time is required.",
  }),
  duration: Joi.number().integer().min(1).required().messages({
    "number.base": "Duration must be a number.",
    "number.integer": "Duration must be an integer value.",
    "number.min": "Duration must be at least 1 minute.",
    "any.required": "Duration is required.",
  }),
}).messages({
  "object.base":
    "Each time slot must be an object with startTime, endTime, and duration",
});

export const TherapistWorkingHoursSchema = Joi.object({
  sunday: Joi.array().items(timeSlotSchema).required().messages({
    "array.base": "Sunday must be an array",
    "any.required": "Sunday schedule is required",
  }),

  monday: Joi.array().items(timeSlotSchema).required().messages({
    "array.base": "Monday must be an array",
    "any.required": "Monday schedule is required",
  }),

  tuesday: Joi.array().items(timeSlotSchema).required().messages({
    "array.base": "Tuesday must be an array",
    "any.required": "Tuesday schedule is required",
  }),

  wednesday: Joi.array().items(timeSlotSchema).required().messages({
    "array.base": "Wednesday must be an array",
    "any.required": "Wednesday schedule is required",
  }),

  thursday: Joi.array().items(timeSlotSchema).required().messages({
    "array.base": "Thursday must be an array",
    "any.required": "Thursday schedule is required",
  }),

  friday: Joi.array().items(timeSlotSchema).required().messages({
    "array.base": "Friday must be an array",
    "any.required": "Friday schedule is required",
  }),

  saturday: Joi.array().items(timeSlotSchema).required().messages({
    "array.base": "Saturday must be an array",
    "any.required": "Saturday schedule is required",
  }),
});

export const TherapistSpecificWorkingHoursSchema = Joi.object({
  date: Joi.string().pattern(dateRegex).required().messages({
    "string.base": "Date must be a string.",
    "string.empty": "Date is required.",
    "string.pattern.base":
      "Date must be in DD-MM-YYYY format (e.g., 13-05-2025).",
    "any.required": "Date is required.",
  }),

  slots: Joi.array().items(timeSlotSchema).min(1).required().messages({
    "array.base": "Slots must be an array.",
    "array.min": "At least one slot must be provided.",
    "any.required": "Slots are required.",
  }),
});

export const TherapistSpecificWorkingHoursUpdateSchema = Joi.object({
  date: Joi.string().pattern(dateRegex).required().messages({
    "string.base": "Date must be a string.",
    "string.empty": "Date is required.",
    "string.pattern.base":
      "Date must be in DD-MM-YYYY format (e.g., 13-05-2025).",
    "any.required": "Date is required.",
  }),

  slots: Joi.array().items(timeSlotSchema).required().messages({
    "array.base": "Slots must be an array.",
    "any.required": "Slots are required.",
  }),
});

export const SessionBookingSchema: Joi.Schema = Joi.object({
  therapistId: Joi.string()
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Therapist Id"),
      "string.base": "Therapist Id must be a string",
      "string.empty": "Therapist Id cannot be empty",
    }),
  email: Joi.string()
    .email()
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Email"),
      "string.base": "Email must be a string",
      "string.empty": "Email cannot be empty",
      "string.email": "Email must be a valid email address",
    }),
  sessionType: Joi.string()
    .valid("introductory", "consultancy")
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Session Type"),
      "string.base": "Session Type must be a string",
      "string.empty": "Session Type cannot be empty",
      "any.only": `"Session Type" must be either "introductory" or "consultancy"`,
    }),
  fromDate: Joi.string().pattern(dateTimeRegex).required().messages({
    "string.pattern.base": `"fromDate" must be in ISO format like "YYYY-MM-DDTHH:mm:ss.sssZ"`,
    "string.empty": `"fromDate" is required`,
    "any.required": `"fromDate" is required`,
  }),
  toDate: Joi.string().pattern(dateTimeRegex).required().messages({
    "string.pattern.base": `"toDate" must be in ISO format like "YYYY-MM-DDTHH:mm:ss.sssZ"`,
    "string.empty": `"toDate" is required`,
    "any.required": `"toDate" is required`,
  }),
  clientCountry: Joi.string()
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Client Country"),
      "string.base": "Client Country must be a string",
      "string.empty": "Client Country cannot be empty",
    }),
  name: Joi.string()
    .pattern(nameRegex)
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Name"),
      "string.base": "Name must be a string",
      "string.empty": "Name cannot be empty",
      "string.pattern.base": "Name can only contain letters and spaces",
    }),
  phone: Joi.string()
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Phone"),
      "string.base": "Phone must be a string",
      "string.empty": "Phone cannot be empty",
    }),
  gender: Joi.string()
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Gender"),
      "string.base": "Gender must be a string",
      "string.empty": "Gender cannot be empty",
    }),
  age: Joi.number()
    .required()
    .positive()
    .messages({
      ...DefaultMessage.defaultRequired("Age"),
      "number.base": "Age must be a number",
      "number.positive": "Age must be a positive number",
    }),
  amount: Joi.number()
    .required()
    .min(0)
    .messages({
      ...DefaultMessage.defaultRequired("Amount"),
      "number.base": "Amount must be a number",
      "number.min": "Amount must be at least 0.",
    }),
  description: Joi.string()
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Description"),
      "string.base": "Description must be a string",
      "string.empty": "Description cannot be empty",
    }),
  location: Joi.string()
    .valid("online", "offline")
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("Location"),
      "string.base": "Location must be a string",
      "string.empty": "Location cannot be empty",
    }),
  timezone: Joi.string().optional().messages({
    "string.base": "Time Zone must be a string",
    "string.empty": "Time Zone cannot be empty",
  }),
  isBefore: Joi.boolean()
    .required()
    .messages({
      ...DefaultMessage.defaultRequired("isBefore"),
      "boolean.base": "isBefore must be a boolean value",
    }),
});

export const TherapistAnalyticsSchema: Joi.Schema = Joi.object({
  fromDate: Joi.string().pattern(dateTimeRegex).required().messages({
    "string.pattern.base": `"fromDate" must be in ISO format like "YYYY-MM-DDTHH:mm:ss.sssZ"`,
    "string.empty": `"fromDate" is required`,
    "any.required": `"fromDate" is required`,
  }),
  toDate: Joi.string().pattern(dateTimeRegex).required().messages({
    "string.pattern.base": `"toDate" must be in ISO format like "YYYY-MM-DDTHH:mm:ss.sssZ"`,
    "string.empty": `"toDate" is required`,
    "any.required": `"toDate" is required`,
  }),
});